{"name": "bougret-geothermal", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@iconify-json/icon-park-outline": "^1.2.2", "@iconify-json/line-md": "^1.2.8", "@iconify-json/ph": "^1.2.2", "@nuxt/image": "1.10.0", "@una-ui/nuxt": "^0.56.1", "nuxt": "^3.17.5", "vue": "^3.5.14", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/lucide": "^1.2.44", "@iconify-json/radix-icons": "^1.2.2", "@iconify-json/tabler": "^1.2.18"}}