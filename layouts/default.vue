<script setup>
const mobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}
</script>

<template>
    <div class="min-h-screen bg-white">
        <!-- Modern Navigation Header -->
        <header class="absolute top-0 w-full z-50 bg-white/10 backdrop-blur-md border-b border-white/20">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex items-center justify-between h-20">
                    <!-- Logo -->
                    <NuxtLink to="/" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <span class="font-black text-2xl tracking-wider text-white">SUERGY</span>
                    </NuxtLink>

                    <!-- Navigation -->
                    <nav class="hidden md:flex items-center space-x-8">
                        <NuxtLink to="/" class="text-white hover:text-primary-400 transition-colors font-medium">
                            Home
                        </NuxtLink>
                        <NuxtLink to="/about" class="text-white hover:text-primary-400 transition-colors font-medium">
                            About
                        </NuxtLink>
                        <NuxtLink to="/events" class="text-white hover:text-primary-400 transition-colors font-medium">
                            Projects
                        </NuxtLink>
                        <NuxtLink to="/contact" class="text-white hover:text-primary-400 transition-colors font-medium">
                            Contact
                        </NuxtLink>
                        <button class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-full font-medium transition-colors">
                            Get Started
                        </button>
                    </nav>

                    <!-- Mobile menu button -->
                    <button @click="toggleMobileMenu" class="md:hidden text-white">
                        <svg v-if="!mobileMenuOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        <svg v-else class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Mobile Navigation Menu -->
                <div v-if="mobileMenuOpen" class="md:hidden bg-white/95 backdrop-blur-md border-t border-gray-200">
                    <div class="px-4 py-6 space-y-4">
                        <NuxtLink to="/" @click="mobileMenuOpen = false" class="block text-gray-900 hover:text-primary-600 transition-colors font-medium">
                            Home
                        </NuxtLink>
                        <NuxtLink to="/about" @click="mobileMenuOpen = false" class="block text-gray-900 hover:text-primary-600 transition-colors font-medium">
                            About
                        </NuxtLink>
                        <NuxtLink to="/events" @click="mobileMenuOpen = false" class="block text-gray-900 hover:text-primary-600 transition-colors font-medium">
                            Projects
                        </NuxtLink>
                        <NuxtLink to="/contact" @click="mobileMenuOpen = false" class="block text-gray-900 hover:text-primary-600 transition-colors font-medium">
                            Contact
                        </NuxtLink>
                        <button class="w-full bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition-colors">
                            Get Started
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main>
            <slot />
        </main>
    </div>
</template>

<style scoped>
/* Clean modern layout styles */
</style>