<script setup>
const accordionItems = [
  {
    label: 'Quel est le projet ?',
    content: 'Flowbite is an open-source library of interactive components built on top of Tailwind CSS including buttons, dropdowns, modals, navbars, and more. Check out this guide to learn how to get started and start developing websites even faster with components on top of Tailwind CSS.',
    defaultOpen: true,
  },
  {
    label: 'Quelle est notre mission ?',
    content: 'Flowbite is first conceptualized and designed using the Figma software so everything you see in the library has a design equivalent in our Figma file. Check out the Figma design system based on the utility classes from Tailwind CSS and components from Flowbite.',
  },
  {
    label: 'Quels sont nos engagements ?',
    content: 'The main difference is that the core components from Flowbite are open source under the MIT license, whereas Tailwind UI is a paid product. Another difference is that Flowbite relies on smaller and standalone components, whereas Tailwind UI offers sections of pages. However, we actually recommend using both Flowbite, Flowbite Pro, and even Tailwind UI as there is no technical reason stopping you from using the best of two worlds.',
  },
]
</script>

<template>
  <section class="h-full xl:w-[50%] md:w-[60%] sm:w-[70%] w-full grid grid-cols-1 mt-6 mb-6 ml-4 mr-4">
    <UAccordion class="text-black" :items="accordionItems" />
  </section>
</template>
